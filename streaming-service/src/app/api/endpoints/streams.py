from fastapi import APIRouter, Depends, HTTPException, status
from typing import List

from app.services.stream_management_service import StreamManagementService
from app.schemas.stream_schema import StreamCreate, StreamView
from app.core.dependencies import get_stream_management_service

router = APIRouter()

@router.post("/", status_code=status.HTTP_201_CREATED, response_model=StreamView)
def create_stream(
    stream_in: StreamCreate,
    service: StreamManagementService = Depends(get_stream_management_service),
):
    """
    Endpoint to create a new stream source.
    """
    try:
        stream = service.create_new_stream(
            name=stream_in.name,
            rtsp_url=stream_in.rtsp_url,
            description=stream_in.description
        )
        return stream
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e),
        )

@router.get("/", response_model=List[StreamView])
def get_all_streams(
    service: StreamManagementService = Depends(get_stream_management_service),
):
    """
    Endpoint to retrieve all configured stream sources.
    """
    return service.get_all_streams()

# TODO: Add endpoints for DELETE /streams/{name}, GET /streams/{name}, etc.
@router.get("/{name}", response_model=StreamView)
def get_stream_by_name(name: str, service: StreamManagementService = Depends(get_stream_management_service)):
    stream = service.get_stream_by_name(name)
    if not stream:
        raise HTTPException(status_code=404, detail="Stream not found")
    return stream
