import httpx
from .base import IMediaMTXClient
from app.core.config import settings

class MediaMTXHttpClient(IMediaMTXClient):
    """
    A concrete implementation of the IMediaMTXClient that communicates
    with the MediaMTX API over HTTP.
    (Liskov Substitution Principle: This can be swapped with any other IMediaMTXClient)
    """
    def __init__(self, api_url: str):
        # It's good practice to use a persistent client for performance
        self._client = httpx.Client(base_url=api_url)

    def add_rtsp_source(self, name: str, rtsp_url: str):
        # Refers to MediaMTX v2/v3 API for adding/updating a path
        api_path = f"/v3/config/paths/add/{name}"
        payload = {
            "source": rtsp_url,
            "sourceOnDemand": True,
            "sourceOnDemandStartTimeout": "10s",
            "sourceOnDemandCloseAfter": "10s",
        }
        try:
            print(f"CLIENT: Sending request to MediaMTX to add path '{name}'")
            response = self._client.post(api_path, json=payload)
            response.raise_for_status()  # Raise an exception for 4xx or 5xx status codes
            print(f"CLIENT: Successfully added path '{name}' to MediaMTX.")
        except httpx.RequestError as e:
            print(f"CLIENT-ERROR: An error occurred while requesting {e.request.url!r}.")
            # In a real app, you'd have more robust error handling/logging
            raise

    def remove_source(self, name: str):
        api_path = f"/v3/config/paths/remove/{name}"
        try:
            print(f"CLIENT: Sending request to MediaMTX to remove path '{name}'")
            response = self._client.post(api_path)
            response.raise_for_status()
            print(f"CLIENT: Successfully removed path '{name}' from MediaMTX.")
        except httpx.RequestError as e:
            print(f"CLIENT-ERROR: An error occurred while requesting {e.request.url!r}.")
            raise