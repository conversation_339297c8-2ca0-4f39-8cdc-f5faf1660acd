from abc import ABC, abstractmethod

class IMediaMTXClient(ABC):
    """
    Abstract interface for a client that interacts with a media server like MediaMTX.
    This defines the contract for what our application expects from a media server client.
    (Interface Segregation Principle)
    """
    @abstractmethod
    def add_rtsp_source(self, name: str, rtsp_url: str):
        """Adds a new RTSP source path to the media server."""
        pass

    @abstractmethod
    def remove_source(self, name: str):
        """Removes a source path from the media server."""
        pass