from pydantic import BaseModel
from typing import Optional
from uuid import UUID

# Pydantic models define the data shapes for your API (the "contract").
# This decouples the API interface from your internal domain models.

class StreamBase(BaseModel):
    name: str
    rtsp_url: str
    description: Optional[str] = None

class StreamCreate(StreamBase):
    # This is the schema used for creating a new stream via the API.
    pass

class StreamView(StreamBase):
    # This is the schema used for returning stream data from the API.
    id: UUID
    enabled: bool

    class Config:
        # Allows Pydantic to map ORM models (like our dataclass) to this schema
        from_attributes = True