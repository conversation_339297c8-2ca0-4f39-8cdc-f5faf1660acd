from typing import List, Optional
from app.domain.stream import Stream
from app.repositories.base import IStreamRepository
from app.clients.base import IMediaMTXClient

class StreamManagementService:
    """
    This service contains the core business logic.
    It orchestrates the interaction between repositories (data) and clients (external services).
    It depends only on abstractions, not concrete classes.
    (Dependency Inversion Principle)
    """
    def __init__(self, repository: IStreamRepository, mediamtx_client: IMediaMTXClient):
        self._repository = repository
        self._mediamtx_client = mediamtx_client
        print("StreamManagementService Initialized.")
        # self._initialize_streams_from_repo() # Optional: sync on startup

    def create_new_stream(self, name: str, rtsp_url: str, description: Optional[str] = None) -> Stream:
        """Orchestrates the creation of a new stream."""
        print(f"SERVICE: Attempting to create stream '{name}'...")
        if self._repository.get_by_name(name):
            raise ValueError(f"Stream with name '{name}' already exists.")

        new_stream = Stream(name=name, rtsp_url=rtsp_url, description=description)

        self._repository.add(new_stream)
        self._mediamtx_client.add_rtsp_source(name=new_stream.name, rtsp_url=new_stream.rtsp_url)
        print(f"SERVICE: Successfully created and registered stream '{name}'.")
        return new_stream

    def get_all_streams(self) -> List[Stream]:
        """Retrieves all streams from the repository."""
        print("SERVICE: Retrieving all streams.")
        return self._repository.get_all()

    def _initialize_streams_from_repo(self):
        """On startup, ensure MediaMTX has all streams from our persistent storage."""
        print("SERVICE: Syncing repository state to MediaMTX on startup...")
        all_streams = self.get_all_streams()
        for stream in all_streams:
            if stream.enabled:
                self._mediamtx_client.add_rtsp_source(stream.name, stream.rtsp_url)
        print("SERVICE: Startup sync complete.")