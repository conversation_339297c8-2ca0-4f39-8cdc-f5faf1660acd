from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID
from app.domain.stream import Stream

class IStreamRepository(ABC):
    """
    Abstract interface for a repository that handles persistence of Stream objects.
    The rest of the application will depend on this interface, not a concrete db implementation.
    (Dependency Inversion Principle)
    """
    @abstractmethod
    def add(self, stream: Stream):
        """Adds a new stream to the repository."""
        pass

    @abstractmethod
    def get_by_name(self, name: str) -> Optional[Stream]:
        """Retrieves a stream by its unique name."""
        pass

    @abstractmethod
    def get_all(self) -> List[Stream]:
        """Retrieves all streams from the repository."""
        pass

    @abstractmethod
    def delete(self, name: str) -> bool:
        """Deletes a stream by its name. Returns True if successful."""
        pass