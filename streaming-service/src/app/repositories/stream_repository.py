import json
from pathlib import Path
from typing import Dict, List, Optional
from app.domain.stream import Stream
from .base import IStreamRepository

class FileStreamRepository(IStreamRepository):
    """
    A concrete implementation of the stream repository that uses a local JSON file for storage.
    """
    def __init__(self, storage_path: str):
        self._storage_path = Path(storage_path)
        self._streams: Dict[str, Stream] = self._load()

    def _load(self) -> Dict[str, Stream]:
        if not self._storage_path.exists():
            return {}
        with self._storage_path.open('r') as f:
            data = json.load(f)
            return {
                name: Stream(**props)
                for name, props in data.items()
            }

    def _save(self):
        # Ensure the parent directory exists
        self._storage_path.parent.mkdir(parents=True, exist_ok=True)
        with self._storage_path.open('w') as f:
            # Convert Stream objects to dictionaries for JSON serialization
            serializable_data = {
                name: stream.__dict__ for name, stream in self._streams.items()
            }
            json.dump(serializable_data, f, indent=4, default=str) # Use default=str for UUID

    def add(self, stream: Stream):
        self._streams[stream.name] = stream
        self._save()
        print(f"REPO: Added/updated stream '{stream.name}' in {self._storage_path}")

    def get_by_name(self, name: str) -> Optional[Stream]:
        return self._streams.get(name)

    def get_all(self) -> List[Stream]:
        return list(self._streams.values())

    def delete(self, name: str) -> bool:
        if name in self._streams:
            del self._streams[name]
            self._save()
            print(f"REPO: Deleted stream '{name}' from {self._storage_path}")
            return True
        return False