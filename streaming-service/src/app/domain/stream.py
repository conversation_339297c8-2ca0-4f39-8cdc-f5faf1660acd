from dataclasses import dataclass, field
from typing import Optional
from uuid import UUID, uuid4

@dataclass
class Stream:
    """
    This is the core business object (domain model).
    It is a pure Python object with no dependencies on frameworks or databases.
    (Single Responsibility Principle)
    """
    name: str
    rtsp_url: str
    enabled: bool = True
    id: UUID = field(default_factory=uuid4)
    description: Optional[str] = None