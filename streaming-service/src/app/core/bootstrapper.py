import yaml
from pathlib import Path
from app.services.stream_management_service import StreamManagementService

def bootstrap_from_config(service: StreamManagementService, config_path_str: str):
    """
    Loads initial streams from a YAML config file on application startup.

    This function is designed to be idempotent: it checks if a stream with the
    same name already exists in the repository before trying to add it. This
    prevents duplicate entries if the application restarts.
    """
    config_path = Path(config_path_str)
    if not config_path.exists():
        print(f"BOOTSTRAP: Config file not found at {config_path_str}, skipping.")
        return

    with config_path.open('r') as f:
        streams_to_load = yaml.safe_load(f)

    if not streams_to_load:
        print("BOOTSTRAP: No streams found in config file.")
        return

    print(f"BOOTSTRAP: Found {len(streams_to_load)} streams in config file.")
    for stream_data in streams_to_load:
        name = stream_data.get("name")
        if not name:
            continue

        # Check if the stream already exists to avoid duplication on restart
        existing_stream = service._repository.get_by_name(name)
        if existing_stream:
            print(f"BOOTSTRAP: Stream '{name}' already exists in repository, skipping.")
            continue

        # If it doesn't exist, create it.
        try:
            print(f"BOOTSTRAP: Creating stream '{name}'...")
            service.create_new_stream(
                name=name,
                rtsp_url=stream_data.get("rtsp_url"),
                description=stream_data.get("description")
            )
        except ValueError as e:
            print(f"BOOTSTRAP-ERROR: Could not create stream '{name}'. Reason: {e}")