from functools import lru_cache
from app.core.config import settings
from app.repositories.base import IStreamRepository
from app.clients.base import IMediaMTXClient
from app.services.stream_management_service import StreamManagementService

# Import the concrete implementations
from app.repositories.stream_repository import FileStreamRepository
from app.clients.mediamtx_client import MediaMTXHttpClient

# This is a key part of Dependency Inversion.
# We can easily swap out the concrete classes here without touching business logic.

@lru_cache()
def get_stream_repository() -> IStreamRepository:
    """
    Provides the configured stream repository implementation.
    """
    if settings.REPOSITORY_TYPE == "file":
        return FileStreamRepository(storage_path=settings.STREAM_CONFIG_FILE_PATH)
    # elif settings.REPOSITORY_TYPE == "database":
    #     return PostgresStreamRepository() # Example for the future
    else:
        raise ValueError(f"Unknown repository type: {settings.REPOSITORY_TYPE}")

@lru_cache()
def get_mediamtx_client() -> IMediaMTXClient:
    """
    Provides an instance of the MediaMTX client.
    """
    return MediaMTXHttpClient(api_url=settings.MEDIAMTX_API_URL)


def get_stream_management_service() -> StreamManagementService:
    """
    This function creates an instance of our main service, injecting
    the repository and client dependencies. FastAPI will call this
    for endpoints that depend on the service.
    """
    return StreamManagementService(
        repository=get_stream_repository(),
        mediamtx_client=get_mediamtx_client()
    )