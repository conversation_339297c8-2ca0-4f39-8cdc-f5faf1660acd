from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    APP_NAME: str = "Streaming Management Service"
    API_V1_STR: str = "/api/v1"

    # URL for the MediaMTX API. This should be configured via an environment variable.
    MEDIAMTX_API_URL: str = "http://localhost:9997"

    # Storage configuration
    # Options: "file" or "database"
    REPOSITORY_TYPE: str = "file"
    STREAM_CONFIG_FILE_PATH: str = "config/streams_storage.json"

    class Config:
        # This allows pydantic to read variables from a .env file
        env_file = ".env"
        env_file_encoding = "utf-8"

# Create a single, importable instance of the settings
settings = Settings()