from fastapi import FastAPI
from app.core.config import settings
from app.api.endpoints import streams

app = FastAPI(
    title=settings.APP_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Include the router from the streams endpoint module
app.include_router(
    streams.router,
    prefix=f"{settings.API_V1_STR}/streams",
    tags=["streams"]
)

@app.get("/", tags=["root"])
def read_root():
    return {"message": "Welcome to the Streaming Management Service"}