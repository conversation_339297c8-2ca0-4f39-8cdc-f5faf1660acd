import asyncio
from fastapi import FastAPI
import httpx

from app.core.config import settings
from app.api.endpoints import streams
from app.core.bootstrapper import bootstrap_from_config
from app.core.dependencies import get_stream_management_service

app = FastAPI(
    title=settings.APP_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

@app.on_event("startup")
async def on_startup():
    """
    FastAPI startup event handler.

    This function first loads the desired state from the YAML config into the
    repository. Then, it enters a retry loop to sync this entire state with
    the MediaMTX service, ensuring all streams are registered.
    """
    print("MAIN: Application startup initiated...")
    
    # --- Step 1: Load config into repository (desired state) ---
    # This happens once, outside the retry loop.
    service = get_stream_management_service()
    bootstrap_from_config(service, "config/initial_streams.yaml")
    
    # --- Step 2: Sync the desired state with MediaMTX, with retries ---
    max_retries = 10
    retry_delay_seconds = 3

    for attempt in range(max_retries):
        try:
            print(f"MAIN: Syncing repository to MediaMTX (Attempt {attempt + 1}/{max_retries})...")
            
            # Get all streams that should exist
            all_streams = service.get_all_streams()
            if not all_streams:
                print("MAIN: No streams in repository to sync.")
                break # Exit loop if there's nothing to do

            # Attempt to register each one
            for stream in all_streams:
                # We directly use the client to avoid the service-level "already exists" check
                service._mediamtx_client.add_rtsp_source(stream.name, stream.rtsp_url)

            print("MAIN: All streams successfully synced to MediaMTX.")
            # If the entire loop completes without error, we are done.
            return

        except httpx.ConnectError as e:
            print(f"MAIN-ERROR: Could not connect to MediaMTX during sync: {e}")
            if attempt < max_retries - 1:
                print(f"MAIN: Retrying in {retry_delay_seconds} seconds...")
                await asyncio.sleep(retry_delay_seconds)
            else:
                print("MAIN-FATAL: Max retries reached. Could not sync with MediaMTX.")
                # Depending on desired behavior, you could raise the exception
                # to stop the application from starting completely.
                # raise e

# Include the router from the streams endpoint module
app.include_router(
    streams.router,
    prefix=f"{settings.API_V1_STR}/streams",
    tags=["streams"]
)

@app.get("/", tags=["root"])
def read_root():
    return {"message": "Welcome to the Streaming Management Service"}