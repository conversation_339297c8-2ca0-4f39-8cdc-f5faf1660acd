# Note: The 'version' attribute is obsolete in modern Docker Compose and has been removed.

services:
  # Our custom streaming management service
  streaming-service:
    build:
      context: . # Looks for the Dockerfile in the current directory
    ports:
      # Exposes our service's API port (8000) to the host machine
      - "8000:8000"
    volumes:
    
      - ./src/app:/usr/src/app/app
      - ./config:/usr/src/app/config
    environment:
      # This sets the environment variable inside the container, overriding the
      # default in the config.py. 'mediamtx' is the hostname of the other service.
      - MEDIAMTX_API_URL=http://mediamtx:9997
    depends_on:
      # Ensures that mediamtx is started before our service
      - mediamtx
    networks:
      - stream-net

  # The MediaMTX workhorse container
  mediamtx:
    image: bluenviron/mediamtx:latest
    ports:
      - "8554:8554"  # RTSP port
      - "8888:8888"  # HLS port
      - "8889:8889"  # WebRTC port
      - "9997:9997"  # The crucial HTTP API port
    networks:
      - stream-net

networks:
  # Defines the shared network that allows services to communicate by name
  stream-net:
    driver: bridge