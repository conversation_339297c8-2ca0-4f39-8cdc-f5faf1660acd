# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /usr/src/app

# Set environment variables to prevent Python from writing .pyc files and to buffer output
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# --- FIX IS HERE ---
# By setting the PYTHONPATH, we tell the Python interpreter to look for modules
# in the /usr/src/app directory, which is where our 'app' package will be located.
ENV PYTHONPATH="/usr/src/app"

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy the entire src directory into the working directory.
# This will create a structure like /usr/src/app/app/...
COPY ./src/app /usr/src/app/app
COPY ./config /usr/src/app/config

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application using uvicorn
# We use 'app.main:app' because 'app' is now correctly found in the PYTHONPATH
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]